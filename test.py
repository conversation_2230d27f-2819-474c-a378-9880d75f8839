import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.providers.google.cloud.operators.bigquery import BigQueryOperator
from airflow.providers.google.cloud.transfers.gcs_to_bigquery import GCSToBigQueryOperator
import sqlx

# =============================================================================
# 1. CUSTOMER DATA PLATFORM (CDP) - PANDAS DATA MANIPULATION
# =============================================================================

class CDPDataProcessor:
    """Customer Data Platform data processing using pandas"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def clean_customer_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize customer data for CDP"""
        
        # Remove duplicates based on customer_id
        df = df.drop_duplicates(subset=['customer_id'], keep='last')
        
        # Standardize email addresses
        df['email'] = df['email'].str.lower().str.strip()
        
        # Clean phone numbers
        df['phone'] = df['phone'].str.replace(r'[^\d+]', '', regex=True)
        
        # Standardize names
        df['first_name'] = df['first_name'].str.title().str.strip()
        df['last_name'] = df['last_name'].str.title().str.strip()
        
        # Handle missing values strategically
        df['registration_date'] = pd.to_datetime(df['registration_date'])
        df['last_activity_date'] = pd.to_datetime(df['last_activity_date'])
        
        # Fill missing last activity with registration date
        df['last_activity_date'] = df['last_activity_date'].fillna(df['registration_date'])
        
        self.logger.info(f"Cleaned {len(df)} customer records")
        return df
    
    def calculate_customer_metrics(self, customers_df: pd.DataFrame, 
                                 transactions_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate key customer metrics for CDP"""
        
        # Customer transaction summary
        customer_metrics = transactions_df.groupby('customer_id').agg({
            'transaction_amount': ['sum', 'mean', 'count'],
            'transaction_date': ['min', 'max']
        }).round(2)
        
        # Flatten column names
        customer_metrics.columns = ['_'.join(col) for col in customer_metrics.columns]
        customer_metrics = customer_metrics.rename(columns={
            'transaction_amount_sum': 'total_spent',
            'transaction_amount_mean': 'avg_transaction',
            'transaction_amount_count': 'transaction_count',
            'transaction_date_min': 'first_purchase',
            'transaction_date_max': 'last_purchase'
        })
        
        # Calculate customer lifetime value and recency
        current_date = datetime.now()
        customer_metrics['days_since_last_purchase'] = (
            current_date - pd.to_datetime(customer_metrics['last_purchase'])
        ).dt.days
        
        # Customer segmentation
        customer_metrics['customer_segment'] = pd.cut(
            customer_metrics['total_spent'],
            bins=[0, 1000, 5000, 10000, float('inf')],
            labels=['Bronze', 'Silver', 'Gold', 'Platinum']
        )
        
        # Merge with customer data
        cdp_data = customers_df.merge(
            customer_metrics.reset_index(), 
            on='customer_id', 
            how='left'
        )
        
        # Fill NaN for customers with no transactions
        cdp_data[['total_spent', 'transaction_count']] = cdp_data[
            ['total_spent', 'transaction_count']
        ].fillna(0)
        
        return cdp_data
    
    def create_customer_360_view(self, customers_df: pd.DataFrame,
                               transactions_df: pd.DataFrame,
                               support_tickets_df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive 360-degree customer view"""
        
        # Base customer metrics
        customer_360 = self.calculate_customer_metrics(customers_df, transactions_df)
        
        # Support interaction metrics
        support_metrics = support_tickets_df.groupby('customer_id').agg({
            'ticket_id': 'count',
            'resolution_time_hours': 'mean',
            'satisfaction_score': 'mean',
            'created_date': 'max'
        }).rename(columns={
            'ticket_id': 'total_tickets',
            'resolution_time_hours': 'avg_resolution_time',
            'satisfaction_score': 'avg_satisfaction',
            'created_date': 'last_support_contact'
        })
        
        # Merge support data
        customer_360 = customer_360.merge(
            support_metrics.reset_index(),
            on='customer_id',
            how='left'
        )
        
        # Calculate customer health score
        customer_360['customer_health_score'] = self._calculate_health_score(customer_360)
        
        return customer_360
    
    def _calculate_health_score(self, df: pd.DataFrame) -> pd.Series:
        """Calculate customer health score (0-100)"""
        score = pd.Series(50, index=df.index)  # Base score
        
        # Recency factor
        score += np.where(df['days_since_last_purchase'] <= 30, 20, 0)
        score += np.where(df['days_since_last_purchase'] <= 90, 10, 0)
        
        # Transaction frequency
        score += np.where(df['transaction_count'] >= 10, 15, 0)
        score += np.where(df['transaction_count'] >= 5, 10, 0)
        
        # Support satisfaction
        score += np.where(df['avg_satisfaction'] >= 4.0, 10, 0)
        
        # Penalize high support volume
        score -= np.where(df['total_tickets'] > 5, 10, 0)
        
        return np.clip(score, 0, 100)

# =============================================================================
# 2. GCP AIRFLOW DATA PIPELINES WITH SQLX
# =============================================================================

# Default DAG arguments
default_args = {
    'owner': 'data-engineering-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False
}

def extract_customer_data(**context):
    """Extract customer data from various sources"""
    import pandas as pd
    from google.cloud import bigquery
    
    client = bigquery.Client()
    
    # Extract from multiple sources
    queries = {
        'customers': """
            SELECT customer_id, email, first_name, last_name, phone, 
                   registration_date, last_activity_date, customer_status
            FROM `project.raw_data.customers`
            WHERE DATE(last_activity_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
        """,
        'transactions': """
            SELECT customer_id, transaction_id, transaction_amount, 
                   transaction_date, product_category
            FROM `project.raw_data.transactions`
            WHERE DATE(transaction_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
        """,
        'support_tickets': """
            SELECT customer_id, ticket_id, created_date, resolution_time_hours,
                   satisfaction_score, issue_category
            FROM `project.raw_data.support_tickets`
            WHERE DATE(created_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 180 DAY)
        """
    }
    
    extracted_data = {}
    for table_name, query in queries.items():
        df = client.query(query).to_dataframe()
        
        # Save to GCS for next step
        gcs_path = f"gs://cdp-data-bucket/staging/{table_name}_{context['ds']}.parquet"
        df.to_parquet(gcs_path, index=False)
        
        extracted_data[table_name] = {
            'records': len(df),
            'gcs_path': gcs_path
        }
        
        logging.info(f"Extracted {len(df)} records from {table_name}")
    
    return extracted_data

def transform_cdp_data(**context):
    """Transform data using pandas for CDP"""
    import pandas as pd
    
    # Get extraction results
    extraction_results = context['task_instance'].xcom_pull(task_ids='extract_customer_data')
    
    # Load data from GCS
    customers_df = pd.read_parquet(extraction_results['customers']['gcs_path'])
    transactions_df = pd.read_parquet(extraction_results['transactions']['gcs_path'])
    support_df = pd.read_parquet(extraction_results['support_tickets']['gcs_path'])
    
    # Initialize CDP processor
    cdp_processor = CDPDataProcessor()
    
    # Clean customer data
    customers_clean = cdp_processor.clean_customer_data(customers_df)
    
    # Create 360-degree customer view
    customer_360 = cdp_processor.create_customer_360_view(
        customers_clean, transactions_df, support_df
    )
    
    # Data quality validation
    quality_checks = {
        'total_customers': len(customer_360),
        'customers_with_transactions': len(customer_360[customer_360['transaction_count'] > 0]),
        'null_emails': customer_360['email'].isnull().sum(),
        'duplicate_emails': customer_360['email'].duplicated().sum()
    }
    
    # Fail if data quality issues
    if quality_checks['null_emails'] > len(customer_360) * 0.05:
        raise ValueError(f"Too many null emails: {quality_checks['null_emails']}")
    
    if quality_checks['duplicate_emails'] > 0:
        raise ValueError(f"Duplicate emails found: {quality_checks['duplicate_emails']}")
    
    # Save transformed data
    output_path = f"gs://cdp-data-bucket/processed/customer_360_{context['ds']}.parquet"
    customer_360.to_parquet(output_path, index=False)
    
    logging.info(f"Transformed data saved: {quality_checks}")
    return {'output_path': output_path, 'quality_checks': quality_checks}

def load_to_cdp_tables(**context):
    """Load processed data to CDP tables using SQLX"""
    
    # SQLX transformation queries
    sqlx_queries = {
        'customer_master': """
            SELECT 
                customer_id,
                email,
                CONCAT(first_name, ' ', last_name) as full_name,
                phone,
                registration_date,
                last_activity_date,
                customer_status,
                customer_segment,
                customer_health_score,
                CURRENT_TIMESTAMP() as last_updated
            FROM `project.staging.customer_360_{{ ds_nodash }}`
        """,
        
        'customer_metrics': """
            SELECT 
                customer_id,
                total_spent,
                avg_transaction,
                transaction_count,
                days_since_last_purchase,
                total_tickets,
                avg_satisfaction,
                first_purchase,
                last_purchase,
                CURRENT_TIMESTAMP() as calculated_at
            FROM `project.staging.customer_360_{{ ds_nodash }}`
            WHERE total_spent > 0
        """,
        
        'customer_segments': """
            SELECT 
                customer_segment,
                COUNT(*) as customer_count,
                AVG(total_spent) as avg_spent,
                AVG(customer_health_score) as avg_health_score,
                '{{ ds }}' as snapshot_date
            FROM `project.staging.customer_360_{{ ds_nodash }}`
            GROUP BY customer_segment
        """
    }
    
    return sqlx_queries

# =============================================================================
# 3. LEAD MANAGEMENT AIRFLOW DAGS
# =============================================================================

def process_lead_scoring(**context):
    """Process and score leads using pandas"""
    import pandas as pd
    from google.cloud import bigquery
    
    client = bigquery.Client()
    
    # Extract leads data
    leads_query = """
        SELECT lead_id, email, phone, source, created_date, 
               last_interaction_date, interaction_count, 
               product_interest, company_size, industry
        FROM `project.raw_data.leads`
        WHERE DATE(created_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
    """
    
    leads_df = client.query(leads_query).to_dataframe()
    
    # Lead scoring algorithm
    leads_df['lead_score'] = 0
    
    # Source scoring
    source_scores = {
        'website': 20, 'referral': 30, 'social_media': 15,
        'email_campaign': 25, 'trade_show': 35, 'cold_call': 10
    }
    leads_df['lead_score'] += leads_df['source'].map(source_scores).fillna(0)
    
    # Interaction frequency scoring
    leads_df['lead_score'] += np.minimum(leads_df['interaction_count'] * 5, 30)
    
    # Recency scoring
    days_since_interaction = (
        datetime.now() - pd.to_datetime(leads_df['last_interaction_date'])
    ).dt.days
    leads_df['lead_score'] += np.where(days_since_interaction <= 3, 25, 0)
    leads_df['lead_score'] += np.where(days_since_interaction <= 7, 15, 0)
    
    # Company size scoring
    size_scores = {'Enterprise': 40, 'Mid-Market': 30, 'SMB': 20, 'Startup': 15}
    leads_df['lead_score'] += leads_df['company_size'].map(size_scores).fillna(0)
    
    # Lead qualification
    leads_df['lead_grade'] = pd.cut(
        leads_df['lead_score'],
        bins=[0, 30, 60, 80, 100],
        labels=['Cold', 'Warm', 'Hot', 'Qualified']
    )
    
    # Save scored leads
    output_path = f"gs://leads-bucket/scored_leads_{context['ds']}.parquet"
    leads_df.to_parquet(output_path, index=False)
    
    logging.info(f"Processed {len(leads_df)} leads")
    return {'leads_processed': len(leads_df), 'output_path': output_path}

# =============================================================================
# 4. AIRFLOW DAG DEFINITIONS
# =============================================================================

# CDP Data Pipeline DAG
cdp_dag = DAG(
    'cdp_data_pipeline',
    default_args=default_args,
    description='Customer Data Platform daily processing',
    schedule_interval='0 3 * * *',  # Daily at 3 AM
    max_active_runs=1,
    tags=['cdp', 'customer-data', 'daily']
)

# CDP Tasks
extract_task = PythonOperator(
    task_id='extract_customer_data',
    python_callable=extract_customer_data,
    dag=cdp_dag
)

transform_task = PythonOperator(
    task_id='transform_cdp_data',
    python_callable=transform_cdp_data,
    dag=cdp_dag
)

# BigQuery load using SQLX patterns
load_customer_master = BigQueryOperator(
    task_id='load_customer_master',
    sql="""
        CREATE OR REPLACE TABLE `project.cdp.customer_master` AS
        SELECT 
            customer_id,
            email,
            CONCAT(first_name, ' ', last_name) as full_name,
            phone,
            registration_date,
            customer_segment,
            customer_health_score,
            CURRENT_TIMESTAMP() as last_updated
        FROM `project.staging.customer_360_{{ ds_nodash }}`
    """,
    use_legacy_sql=False,
    dag=cdp_dag
)

# Lead Management DAG
lead_dag = DAG(
    'lead_management_pipeline',
    default_args=default_args,
    description='Lead scoring and management pipeline',
    schedule_interval='0 */4 * * *',  # Every 4 hours
    max_active_runs=1,
    tags=['leads', 'scoring', 'sales']
)

lead_scoring_task = PythonOperator(
    task_id='process_lead_scoring',
    python_callable=process_lead_scoring,
    dag=lead_dag
)

# Set dependencies
extract_task >> transform_task >> load_customer_master

# =============================================================================
# 5. RDBMS OPERATIONS AND SQL OPTIMIZATION
# =============================================================================

class RDBMSOperations:
    """RDBMS operations for data engineering tasks"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
    
    def optimize_customer_queries(self) -> Dict[str, str]:
        """Optimized SQL queries for customer data operations"""
        
        return {
            'customer_summary_with_index': """
                -- Optimized query with proper indexing
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_activity_date 
                ON customers(last_activity_date);
                
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_customer_date 
                ON transactions(customer_id, transaction_date);
                
                SELECT 
                    c.customer_id,
                    c.email,
                    c.registration_date,
                    COUNT(t.transaction_id) as transaction_count,
                    COALESCE(SUM(t.transaction_amount), 0) as total_spent,
                    MAX(t.transaction_date) as last_purchase_date
                FROM customers c
                LEFT JOIN transactions t ON c.customer_id = t.customer_id
                WHERE c.last_activity_date >= CURRENT_DATE - INTERVAL '90 days'
                GROUP BY c.customer_id, c.email, c.registration_date
                ORDER BY total_spent DESC;
            """,
            
            'efficient_customer_segmentation': """
                -- Efficient segmentation using window functions
                WITH customer_metrics AS (
                    SELECT 
                        customer_id,
                        SUM(transaction_amount) as total_spent,
                        COUNT(*) as transaction_count,
                        NTILE(4) OVER (ORDER BY SUM(transaction_amount)) as spend_quartile
                    FROM transactions
                    WHERE transaction_date >= CURRENT_DATE - INTERVAL '365 days'
                    GROUP BY customer_id
                )
                SELECT 
                    customer_id,
                    total_spent,
                    transaction_count,
                    CASE spend_quartile
                        WHEN 4 THEN 'Platinum'
                        WHEN 3 THEN 'Gold'
                        WHEN 2 THEN 'Silver'
                        ELSE 'Bronze'
                    END as customer_segment
                FROM customer_metrics;
            """,
            
            'data_quality_monitoring': """
                -- Data quality monitoring queries
                SELECT 
                    'customers' as table_name,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT email) as unique_emails,
                    COUNT(*) - COUNT(email) as null_emails,
                    COUNT(*) - COUNT(DISTINCT customer_id) as duplicate_ids,
                    CURRENT_TIMESTAMP as check_timestamp
                FROM customers
                
                UNION ALL
                
                SELECT 
                    'transactions' as table_name,
                    COUNT(*) as total_records,
                    COUNT(DISTINCT customer_id) as unique_customers,
                    COUNT(*) - COUNT(transaction_amount) as null_amounts,
                    SUM(CASE WHEN transaction_amount < 0 THEN 1 ELSE 0 END) as negative_amounts,
                    CURRENT_TIMESTAMP as check_timestamp
                FROM transactions;
            """
        }

def demonstrate_job_requirements():
    """Demonstrate all key job requirements"""
    
    print("🎯 DEMONSTRATING JOB REQUIREMENTS")
    print("=" * 50)
    
    # 1. Pandas data manipulation for CDP
    print("\n1. 📊 PANDAS DATA MANIPULATION FOR CDP")
    cdp_processor = CDPDataProcessor()
    
    # Sample data
    customers_sample = pd.DataFrame({
        'customer_id': [1, 2, 3, 4, 5],
        'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'first_name': ['john', 'Jane', 'BOB', 'alice', 'Charlie'],
        'last_name': ['DOE', 'smith', 'JOHNSON', 'brown', 'Wilson'],
        'phone': ['(*************', '************', '5551234567', '************', '(555)999-8888'],
        'registration_date': pd.date_range('2023-01-01', periods=5, freq='30D'),
        'last_activity_date': pd.date_range('2024-01-01', periods=5, freq='15D')
    })
    
    cleaned_customers = cdp_processor.clean_customer_data(customers_sample)
    print("✅ Customer data cleaned and standardized")
    print(cleaned_customers[['customer_id', 'email', 'first_name', 'phone']].head())
    
    # 2. Data pipeline maintenance concepts
    print("\n2. 🔧 DATA PIPELINE MAINTENANCE (AIRFLOW + SQLX)")
    print("✅ CDP Pipeline: extract_customer_data -> transform_cdp_data -> load_customer_master")
    print("✅ Lead Pipeline: process_lead_scoring -> load_scored_leads")
    print("✅ Error handling: Retries, data quality checks, email alerts")
    print("✅ SQLX patterns: Templated SQL with proper transformations")
    
    # 3. RDBMS understanding
    print("\n3. 🗄️ RDBMS OPERATIONS")
    rdbms_ops = RDBMSOperations("postgresql://localhost:5432/cdp")
    optimized_queries = rdbms_ops.optimize_customer_queries()
    print("✅ Optimized queries with proper indexing")
    print("✅ Window functions for efficient segmentation")
    print("✅ Data quality monitoring queries")
    
    print("\n🎉 ALL JOB REQUIREMENTS DEMONSTRATED!")
    print("Ready for CDP data manipulation, pipeline maintenance, and lead operations!")

if __name__ == "__main__":
    demonstrate_job_requirements()
